"""
AISHELL-1数据集加载器
"""

import os
import librosa
import soundfile as sf
from typing import List, Tuple, Dict
import pandas as pd
from tqdm import tqdm


class AishellDataLoader:
    """AISHELL-1数据集加载器"""
    
    def __init__(self, data_path: str, sample_rate: int = 16000):
        self.data_path = data_path
        self.sample_rate = sample_rate
        
    def load_wav_scp(self, wav_scp_path: str) -> Dict[str, str]:
        """加载wav.scp文件，返回utterance_id到音频文件路径的映射"""
        wav_dict = {}
        with open(wav_scp_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 2:
                        utt_id = parts[0]
                        wav_path = ' '.join(parts[1:])
                        wav_dict[utt_id] = wav_path
        return wav_dict
    
    def load_text_file(self, text_path: str) -> Dict[str, str]:
        """加载text文件，返回utterance_id到转录文本的映射"""
        text_dict = {}
        with open(text_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(None, 1)  # 只分割第一个空格
                    if len(parts) >= 2:
                        utt_id = parts[0]
                        text = parts[1]
                        text_dict[utt_id] = text
        return text_dict
    
    def load_audio(self, audio_path: str) -> Tuple[List[float], int]:
        """加载音频文件"""
        try:
            # 尝试使用soundfile加载
            audio, sr = sf.read(audio_path)
            if sr != self.sample_rate:
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
            return audio.tolist(), self.sample_rate
        except Exception as e:
            print(f"Error loading audio {audio_path}: {e}")
            return None, None
    
    def get_test_data(self, wav_scp_path: str, text_path: str, max_samples: int = None) -> List[Dict]:
        """获取测试数据"""
        # 加载wav.scp和text文件
        wav_dict = self.load_wav_scp(wav_scp_path)
        text_dict = self.load_text_file(text_path)
        
        # 找到共同的utterance_id
        common_ids = set(wav_dict.keys()) & set(text_dict.keys())
        
        if max_samples:
            common_ids = list(common_ids)[:max_samples]
        
        test_data = []
        print(f"Loading {len(common_ids)} test samples...")
        
        for utt_id in tqdm(common_ids, desc="Loading audio files"):
            wav_path = wav_dict[utt_id]
            reference_text = text_dict[utt_id]
            
            # 处理相对路径
            if not os.path.isabs(wav_path):
                wav_path = os.path.join(self.data_path, wav_path)
            
            audio, sr = self.load_audio(wav_path)
            if audio is not None:
                test_data.append({
                    'utterance_id': utt_id,
                    'audio_path': wav_path,
                    'audio': audio,
                    'sample_rate': sr,
                    'reference_text': reference_text
                })
        
        print(f"Successfully loaded {len(test_data)} samples")
        return test_data
